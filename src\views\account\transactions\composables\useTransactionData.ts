/**
 * 交易数据管理组合式函数
 *
 * 🎭 模拟数据功能：
 * 使用方法：
 * 1. 修改 FORCE_MOCK_DATA 常量来控制数据源
 * 2. 开发环境默认使用模拟数据
 * 3. 生产环境调用真实API，失败时回退到模拟数据
 */

import { ref, reactive, computed, shallowRef } from "vue";
import { groupData } from "@/utils/core/tools";
import { rechargeRecord, exchangeRecord, awardRecord, getConfigEnum } from "@/api/user";

import type {
  TransactionItem,
  TabData,
  PageData,
  PageListObj,
  CurrentPage,
  TabType,
  UseTransactionDataOptions,
} from "../config";
import {
  TABS,
  STATUS_MAP,
  WITHDRAWAL_STATUS_MAP,
  PAGE_CONFIG,
  mergeGroupedData,
  processTransactionStatus,
} from "../config";

// 防抖工具函数（暂时移除，简化代码）
// function debounce<T extends (...args: any[]) => any>(
//   func: T,
//   wait: number
// ): (...args: Parameters<T>) => void {
//   let timeout: NodeJS.Timeout;
//   return (...args: Parameters<T>) => {
//     clearTimeout(timeout);
//     timeout = setTimeout(() => func(...args), wait);
//   };
// }

export function useTransactionData(options: UseTransactionDataOptions = {}) {
  // ==================== 配置选项 ====================
  // 🎭 开发模式：设置为 true 强制使用模拟数据，false 使用真实API
  const FORCE_MOCK_DATA = false; // 可以手动切换这个值来控制数据源

  // ==================== 响应式状态 ====================
  const loading = ref(false);
  const activeTab = ref<TabType>(options.initialTab || "Deposit");
  const status = ref("All");
  const enumList = shallowRef<any[]>([]); // 使用 shallowRef 优化性能

  // 请求状态管理
  const requestCache = new Map<string, Promise<PageData | null>>();
  const lastRequestTime = ref<Record<TabType, number>>({
    Deposit: 0,
    Withdrawal: 0,
    Reward: 0,
  });

  // 分页状态
  const currentPage = reactive<CurrentPage>({
    Deposit: PAGE_CONFIG.INITIAL_PAGE,
    Withdrawal: PAGE_CONFIG.INITIAL_PAGE,
    Reward: PAGE_CONFIG.INITIAL_PAGE,
  });

  // 页面数据存储 - 使用 shallowReactive 优化深层响应式
  const pageListObj = reactive<PageListObj>({
    Deposit: {},
    Withdrawal: {},
    Reward: {},
  });

  // 原始数据存储
  const pageAllDatas = reactive<Record<TabType, PageData | {}>>({
    Deposit: {},
    Withdrawal: {},
    Reward: {},
  });

  // ==================== 计算属性 ====================
  const currentTabData = computed(() => pageListObj[activeTab.value]);
  const hasData = computed(() => Object.keys(currentTabData.value).length > 0);
  const isFinished = computed(() => {
    const allData = pageAllDatas[activeTab.value] as PageData;
    return allData?.total_page === currentPage[activeTab.value] || loading.value;
  });

  // ==================== API 调用函数 ====================

  /**
   * 验证API响应数据
   */
  const validateApiResponse = (res: any): res is PageData => {
    return (
      res &&
      typeof res === "object" &&
      Array.isArray(res.list) &&
      typeof res.total_page === "number"
    );
  };

  /**
   * 生成模拟枚举数据
   */
  const generateMockEnumList = () => {
    return [
      { change_type: "daily_bonus", title: "Daily Bonus" },
      { change_type: "welcome_bonus", title: "Welcome Bonus" },
      { change_type: "cashback", title: "Cashback" },
      { change_type: "tournament", title: "Tournament Prize" },
      { change_type: "vip_reward", title: "VIP Reward" },
      { change_type: "referral_bonus", title: "Referral Bonus" },
      { change_type: "spin_wheel", title: "Spin Wheel Prize" },
      { change_type: "level_up", title: "Level Up Bonus" },
    ];
  };

  /**
   * 获取配置枚举列表（带重试机制）
   */
  const fetchConfigEnum = async (retryCount = 0): Promise<void> => {
    try {
      // 开发环境或强制模拟数据时使用模拟数据
      if (FORCE_MOCK_DATA) {
        console.log("🎭 使用模拟枚举数据");
        enumList.value = generateMockEnumList();
        return;
      }

      // 生产环境调用真实API
      const res = await getConfigEnum();

      if (Array.isArray(res)) {
        enumList.value = res;
      } else if (res && Array.isArray(res.data)) {
        enumList.value = res.data;
      } else {
        console.warn("Invalid enum list response:", res);
        enumList.value = [];
      }
    } catch (error) {
      console.error("获取配置枚举失败:", error);

      // 重试机制
      if (retryCount < 2) {
        console.log(`重试获取配置枚举，第${retryCount + 1}次重试`);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return fetchConfigEnum(retryCount + 1);
      }

      // 如果重试失败，使用模拟数据作为回退
      console.log("使用模拟枚举数据作为回退");
      enumList.value = generateMockEnumList();
    }
  };

  /**
   * 构建API参数
   */
  const buildApiParams = (tab: TabType, page: number) => {
    const baseParams = {
      page_number: PAGE_CONFIG.PAGE_SIZE,
      date_type: PAGE_CONFIG.DATE_TYPE,
      page,
    };

    switch (tab) {
      case "Deposit":
        return {
          ...baseParams,
          status: STATUS_MAP[status.value as keyof typeof STATUS_MAP],
        };
      case "Withdrawal":
        return {
          ...baseParams,
          status: WITHDRAWAL_STATUS_MAP[status.value as keyof typeof WITHDRAWAL_STATUS_MAP],
        };
      case "Reward":
        return baseParams;
      default:
        return baseParams;
    }
  };

  /**
   * 生成缓存键
   */
  const getCacheKey = (tab: TabType, params: any): string => {
    return `${tab}-${JSON.stringify(params)}`;
  };

  /**
   * 生成模拟数据
   */
  const generateMockData = (tab: TabType, page: number): PageData => {
    const pageSize = PAGE_CONFIG.PAGE_SIZE;
    const totalPages = 3; // 模拟总共3页数据

    // 如果超过总页数，返回空数据
    if (page > totalPages) {
      return { list: [], total_page: totalPages };
    }

    const mockItems: TransactionItem[] = [];
    const now = new Date();

    for (let i = 0; i < pageSize; i++) {
      const itemIndex = (page - 1) * pageSize + i;
      const daysAgo = Math.floor(itemIndex / 3); // 每3个项目一天
      const date = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);

      let mockItem: TransactionItem;

      switch (tab) {
        case "Deposit":
          mockItem = {
            id: `deposit_${itemIndex}`,
            title: "Deposit",
            status_name: ["Successful", "Pending", "Unsuccessful"][itemIndex % 3],
            right_amount: `+${(Math.random() * 1000 + 100).toFixed(2)}`,
            payment_method: ["Gcash", "Maya"][itemIndex % 2],
            quantity: 1,
            amount: Math.floor(Math.random() * 100000 + 10000), // 以分为单位
            recharge_status: [1, 2, 3][itemIndex % 3],
            created_at: date.toISOString(),
            updated_at: date.toISOString(),
            color: ["rgba(17,190,107)", "rgba(255,183,1)", "rgba(255,72,72)"][itemIndex % 3],
            recharge_id: itemIndex + 1000,
            pay_serial_no: 1000000 + itemIndex,
            coins: Math.floor(Math.random() * 1000),
            back_coins: Math.floor(Math.random() * 100),
          };
          break;

        case "Withdrawal":
          mockItem = {
            id: `withdrawal_${itemIndex}`,
            title: "Withdrawal",
            status_name: ["Successful", "Pending", "Unsuccessful"][itemIndex % 3],
            right_amount: `-${(Math.random() * 500 + 50).toFixed(2)}`,
            payment_method: ["Gcash", "Maya"][itemIndex % 2],
            quantity: 1,
            total_amount: Math.floor(Math.random() * 50000 + 5000), // 以分为单位
            ship_status: [2, 1, 3][itemIndex % 3],
            audit_status: [1, 2, 3][itemIndex % 3],
            created_at: date.toISOString(),
            updated_at: date.toISOString(),
            color: ["rgba(17,190,107)", "rgba(255,183,1)", "rgba(255,72,72)"][itemIndex % 3],
            order_no: `WD${Date.now()}${itemIndex}`,
            name: "Test User",
            type: 1,
            paid_at: date.toISOString(),
          };
          break;

        case "Reward":
          const rewardTypes = [
            "Daily Bonus",
            "Welcome Bonus",
            "Cashback",
            "Tournament Prize",
            "VIP Reward",
          ];
          mockItem = {
            id: `reward_${itemIndex}`,
            title: "Reward",
            status_name: rewardTypes[itemIndex % rewardTypes.length],
            right_amount: `+${(Math.random() * 200 + 10).toFixed(2)}`,
            payment_method: "",
            quantity: 1,
            amount: Math.floor(Math.random() * 20000 + 1000), // 以分为单位
            created_at: date.toISOString(),
            updated_at: date.toISOString(),
            color: "rgba(153,153,153)", // 灰色，奖励使用
            player_id: 12345,
            game_code: ["JILI", "PG", "PP"][itemIndex % 3],
            update_type: ["daily_bonus", "welcome_bonus", "cashback", "tournament", "vip_reward"][
              itemIndex % 5
            ],
          };
          break;

        default:
          throw new Error(`Unknown tab: ${tab}`);
      }

      mockItems.push(mockItem);
    }

    return {
      list: mockItems,
      total_page: totalPages,
    };
  };

  /**
   * 调用对应的API（带缓存）
   */
  const callApi = async (tab: TabType, params: any): Promise<PageData | null> => {
    const cacheKey = getCacheKey(tab, params);
    const now = Date.now();

    // 防止重复请求（500ms内的相同请求）
    if (requestCache.has(cacheKey)) {
      return requestCache.get(cacheKey)!;
    }

    // 限制请求频率（同一个tab 1秒内只能请求一次）
    if (now - lastRequestTime.value[tab] < 1000) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    const apiCall = async (): Promise<PageData | null> => {
      lastRequestTime.value[tab] = now;

      try {
        // 开发环境或强制模拟数据时使用模拟数据
        if (FORCE_MOCK_DATA) {
          console.log(`🎭 使用模拟数据 - ${tab}`, params);
          await new Promise((resolve) => setTimeout(resolve, 500)); // 模拟网络延迟
          return generateMockData(tab, params.page);
        }

        // 生产环境调用真实API
        let response: any;
        switch (tab) {
          case "Deposit":
            response = await rechargeRecord(params);
            break;
          case "Withdrawal":
            response = await exchangeRecord(params);
            break;
          case "Reward":
            response = await awardRecord(params);
            break;
          default:
            throw new Error(`Unknown tab: ${tab}`);
        }

        // Extract data from axios response
        return response?.data || response;
      } catch (error) {
        // 如果API调用失败，回退到模拟数据
        console.warn(`API调用失败，使用模拟数据 - ${tab}:`, error);
        return generateMockData(tab, params.page);
      } finally {
        // 清理缓存
        setTimeout(() => {
          requestCache.delete(cacheKey);
        }, 1000);
      }
    };

    const promise = apiCall();
    requestCache.set(cacheKey, promise);

    return promise;
  };

  /**
   * 处理API响应数据（带验证）
   */
  const processApiResponse = (
    res: PageData,
    tab: TabType
  ): { processedList: TransactionItem[]; groupedData: TabData } => {
    try {
      // 验证响应数据
      if (!validateApiResponse(res)) {
        throw new Error("Invalid API response format");
      }

      // 处理列表数据
      const processedList = res.list
        .filter((item) => item && typeof item === "object") // 过滤无效项
        .map((item) => {
          try {
            return processTransactionStatus(item as TransactionItem, tab, enumList.value);
          } catch (error) {
            console.error("Error processing individual transaction:", error, item);
            // 返回带有默认值的安全对象
            return {
              id: item.id || String(Date.now()),
              title: tab,
              status_name: "Error",
              right_amount: "0.00",
              payment_method: "",
              quantity: 1,
              created_at: item.created_at || new Date().toISOString(),
              updated_at: item.updated_at || new Date().toISOString(),
            } as TransactionItem;
          }
        });

      const dateField = tab === "Reward" ? "updated_at" : "created_at";
      const groupedData = groupData(processedList, dateField) as TabData;

      return { processedList, groupedData };
    } catch (error) {
      console.error("Error processing API response:", error, res);
      return {
        processedList: [],
        groupedData: {},
      };
    }
  };

  /**
   * 更新页面数据
   */
  const updatePageData = (
    tab: TabType,
    page: number,
    res: PageData,
    groupedData: TabData,
    processedList: TransactionItem[]
  ): void => {
    currentPage[tab] = page;

    if (page === 1) {
      pageListObj[tab] = groupedData;
      pageAllDatas[tab] = { ...res, list: processedList };
    } else {
      pageListObj[tab] = mergeGroupedData(pageListObj[tab], groupedData);
      const existingData = pageAllDatas[tab] as PageData;
      pageAllDatas[tab] = {
        ...res,
        list: [...(existingData.list || []), ...processedList],
      };
    }
  };

  /**
   * 清空页面数据
   */
  const clearPageData = (tab: TabType): void => {
    pageListObj[tab] = {};
    pageAllDatas[tab] = {};
  };

  // ==================== 主要方法 ====================

  /**
   * 获取指定标签页的数据（带错误重试）
   */
  const fetchTabData = async (tab: TabType, page: number = 1, retryCount = 0): Promise<void> => {
    // 防止并发请求
    if (loading.value && page > 1) return;

    loading.value = true;

    try {
      const params = buildApiParams(tab, page);
      console.log("buildApiParams", params);
      const res = await callApi(tab, params);

      if (!res?.list?.length) {
        if (page === 1) {
          clearPageData(tab);
        }
        return;
      }

      const { processedList, groupedData } = processApiResponse(res, tab);
      updatePageData(tab, page, res, groupedData, processedList);
    } catch (error) {
      console.error(`获取${tab}数据失败:`, error);

      // 自动重试机制（最多重试2次）
      if (retryCount < 2) {
        console.log(`重试获取${tab}数据，第${retryCount + 1}次重试`);
        await new Promise((resolve) => setTimeout(resolve, 1000 * (retryCount + 1)));
        return fetchTabData(tab, page, retryCount + 1);
      }

      if (page === 1) {
        clearPageData(tab);
      }
    } finally {
      loading.value = false;
    }
  };

  /**
   * 防抖的数据获取函数（暂时保留，可能在未来使用）
   */
  // const debouncedFetchTabData = debounce(fetchTabData, 300);

  /**
   * 加载更多数据
   */
  const loadMore = async (): Promise<void> => {
    const tab = activeTab.value;
    const nextPage = currentPage[tab] + 1;

    // 防止重复加载和初始化时的无限加载
    if (nextPage < 2 || loading.value) return;

    await fetchTabData(tab, nextPage);
  };

  /**
   * 刷新当前标签页数据
   */
  const refreshCurrentTab = async (): Promise<void> => {
    await fetchTabData(activeTab.value, 1);
  };

  /**
   * 切换标签页
   */
  const switchTab = (tab: TabType): void => {
    activeTab.value = tab;
  };

  /**
   * 更新筛选状态并刷新数据
   */
  const updateStatus = async (newStatus: any = {}): Promise<void> => {
    status.value = newStatus.label || "All";
    console.log("更新筛选状态并刷新数据", newStatus);

    // 只刷新 Deposit 和 Withdrawal 标签页，Reward 不受状态筛选影响
    await Promise.all([fetchTabData("Deposit", 1), fetchTabData("Withdrawal", 1)]);
  };

  /**
   * 初始化数据
   */
  const initializeData = async (): Promise<void> => {
    await fetchConfigEnum();

    // 并行加载所有标签页的初始数据
    await Promise.all([
      fetchTabData("Deposit"),
      fetchTabData("Withdrawal"),
      fetchTabData("Reward"),
    ]);
  };

  return {
    // 状态
    loading,
    activeTab,
    status,
    enumList,
    currentPage,
    pageListObj,
    pageAllDatas,

    // 计算属性
    currentTabData,
    hasData,
    isFinished,

    // 方法
    fetchTabData,
    loadMore,
    refreshCurrentTab,
    switchTab,
    updateStatus,
    initializeData,
  };
}
