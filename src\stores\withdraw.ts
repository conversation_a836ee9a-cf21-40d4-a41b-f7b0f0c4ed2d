import { defineStore } from "pinia";
import {
  getWithdrawAccounts,
  goExchange,
  checkWithdrawRisk,
  confirmWithdrawPassword,
} from "@/api/withdrawal";
import { rechargeWithdraw } from "@/api/deposit";
import { useGlobalStore } from "@/stores/global";
import { useGameStore } from "@/stores/game";
import { showToast } from "vant";
import { CHANEL_TYPE, PRODUCT_TYPE, MAINTENANCETIPCODE } from "@/utils/config/GlobalConstant";
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import router from "@/router";
import { Md5 } from "@/utils/core/Md5";
import { getGlobalDialog } from "@/enter/vant";
import { KycMgr, InGameType } from "@/utils/KycMgr";
import { maskAccountNumber } from "@/utils/core/tools";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import enTranslations from "@/utils/I18n/en.json";
import { METHODS_NAMES, METHODS_ID } from "@/utils/config/GlobalConstant";
import { useVerifyPreconditions } from "@/composables/useVerifyPreconditions";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import { verifyLoginPassword } from "@/utils/HttpProxy";

// 风控信息接口
export interface RiskWithdrawalInfo {
  status: number;
  user_id: string;
  user_total_valid_bet: number;
  target_amount: number;
}

// 提现数据接口
interface WithdrawData {
  min: number;
  max: number;
  sort: number;
  name: string;
}

// 账户信息接口
interface AccountInfo {
  account_id?: string;
  account_no?: string;
  name?: string;
  type?: number;
}

export const useWithdrawStore = defineStore("withdraw", {
  state: () => {
    return {
      // 已选中的账户信息
      selectedAccountInfo: null as AccountInfo | null,
      //弹窗：显示提现弹
      showWithdrawDialog: true,
      //弹窗：显示账户选择列表
      showAccountListDialog: false,
      //弹窗：小程序内金额确认
      showMoneyConfirmDialog: false,
      // 弹窗：1倍流水风控制
      showRiskWithdrawalDialog: false,
      // 弹窗：设置手机号、登录密码
      showVerifyPreconditionsDialog: false,
      // 弹窗：输入登录密码
      showVerifyLoginPasswordDialog: false,
      // 弹窗：密码次数过多限制
      showPaymentPassworLimtDialog: false,
      // 弹窗：KYC 二次提醒
      showKycRemindDialog: false,
      // 账户列表
      accounts: [] as AccountInfo[],
      // 账户列表限制
      accountLimitNum: 10,
      // 提现方式列表
      withdrawData: [] as WithdrawData[],
      // 当前提现方式的可提的最大、最小值
      minAmountNum: null as number | null,
      maxAmountNum: null as number | null,
      // 输入（选中）的充值金额
      selectedAmount: "",
      // 当前银行卡归宿，Maya、Gcash
      curRechangeMethods: "",
      // 错误提示
      errTip: "",
      // 警告提示
      warnTip: "",
      // 1倍流水风控信息
      riskWithdrawalInfo: null as RiskWithdrawalInfo | null,
      // 次数限制 email
      customerEmail: "",
      riskWithdrawalCallBack: () => {},
    };
  },
  getters: {
    // 渠道来源
    CHANEL_TYPE() {
      const globalStore = useGlobalStore();
      return globalStore.channel;
    },
    // 是否小程序端，即 G_CASH、MAYA 端
    isMiniChannel() {
      const globalStore = useGlobalStore();
      const channel = globalStore.channel?.toLowerCase();
      return ["gcash", "maya"].includes(channel);
    },
    $dialog() {
      return getGlobalDialog();
    },
    isNeedLoginPassword() {
      return useGameStore().configData.edit_login_password_switch == "1";
    },
  },
  actions: {
    // 打开提现入口
    async openDialog() {
      const globalStore = useGlobalStore();
      if (!globalStore.token) {
        router.push("/login");
        return;
      }

      // 显示加载状态
      showZLoading();

      try {
        // 并行执行多个异步任务
        const tasks: Promise<any>[] = [
          // 获取提现配置
          this.getWithdrawConfigList(() => {
            this.setWithdrawRangeValue(0);
          }),
        ];

        // 根据渠道类型添加不同的任务
        if (!this.isMiniChannel) {
          // WEB 渠道：并行获取账户数据
          tasks.push(getWithdrawAccounts());
        }

        // 并行执行所有任务
        const results = await Promise.all(tasks);

        // 处理账户数据（如果是 WEB 渠道）
        if (!this.isMiniChannel) {
          const accountResponse = results[1] as any; // getWithdrawAccounts 的结果
          const accountData = accountResponse.data || accountResponse;
          const list = accountData.list || [];

          // 保存账户数据
          this.accounts = list;
          this.accountLimitNum = accountData.total_num;

          if (list && list.length > 0) {
            // 默认选中第一个账户
            this.selectedAccountInfo = {
              ...list[0],
              name: METHODS_NAMES[list[0].type],
            };
            this.curRechangeMethods = METHODS_NAMES[list[0].type];
          }
        } else if (this.isMiniChannel) {
          // 小程序端设置默认账户
          this.selectedAccountInfo = {
            account_no: maskAccountNumber(globalStore.userInfo.phone || ""),
            name: globalStore.channel,
            type: METHODS_ID[globalStore.channel as keyof typeof METHODS_ID],
          };
          this.curRechangeMethods = globalStore.channel;
        }
        // 根据渠道类型和账户情况决定后续操作
        if (!this.isMiniChannel) {
          // 非小程序渠道：检查账户数量决定跳转逻辑
          if (this.accounts && this.accounts.length > 0) {
            // 有提现账户，跳转提现页面
            this.handleRiskWithdrawalDialog(() => {
              this.showWithdrawDialog = true;
            });
          } else {
            // 无提现账户，直接跳转添加提现账户页面
            this.handleRiskWithdrawalDialog(() => {
              router.push("/account/withdraw-account");
            });
          }
        } else if (this.CHANEL_TYPE === CHANEL_TYPE.G_CASH) {
          this.handleRiskWithdrawalDialog(() => {
            this.showWithdrawDialog = true;
          });
        } else if (this.CHANEL_TYPE === CHANEL_TYPE.MAYA) {
          this.handleRiskWithdrawalDialog(() => {
            this.showWithdrawDialog = true;
          });
        }
      } catch (error) {
        console.error("Load withdraw config failed:", error);
        this.withdrawData = [];
        this.accounts = [];
        this.showWithdrawDialog = false;
        showToast("The current network is abnormal, please try again later.");
      } finally {
        // 关闭加载状态
        closeZLoading();
      }
    },
    // 输入框 input事件
    handleCustomAmountInput(event: Event) {
      const input = event.target as HTMLInputElement;
      let value = input.value.replace(/\D/g, "") as string;

      // 如果输入为空，直接处理
      if (!value) {
        this.setSelectedAmount("");
        this.getVaildAmountErr("");
        return;
      }

      // 金额限制：确保不超过最大限额
      const numValue = Number(value);
      if (this.maxAmountNum && this.maxAmountNum > 0 && numValue > this.maxAmountNum) {
        value = this.maxAmountNum.toString();
        // 更新输入框显示值
        input.value = value;
      }

      this.setSelectedAmount(value);
    },
    // 点击选中
    setSelectedAmount(amount: string) {
      this.selectedAmount = amount;
      this.getVaildAmountErr(amount);
    },
    // 校验金额范围提示
    getVaildAmountErr(value: string | number) {
      const min = this.minAmountNum || 0;
      const max = this.maxAmountNum || 0;
      const globalStore = useGlobalStore();
      const availableBalance = globalStore.balance;
      const numValue = Number(value);

      // 重置提示状态
      this.errTip = "";
      this.warnTip = "";

      // 空值处理
      if (value === "" || value === null || value === undefined) {
        this.errTip = `Enter Amount ${min} - ${max}₱`;
        return this.errTip;
      }

      // 无效数值处理
      if (isNaN(numValue) || numValue < 0) {
        this.errTip = "Please enter a valid amount";
        return this.errTip;
      }

      // 余额不足检查（优先级最高）
      if (availableBalance < numValue) {
        this.errTip = "Insufficient Balance.";
        return this.errTip;
      }

      // 金额范围验证
      if (numValue < min) {
        this.errTip = `The minimum amount is ${min}₱`;
      } else if (numValue > max) {
        this.errTip = `The maximum amount is ${max}₱`;
      } else if (numValue === max) {
        // 达到最大限额时显示警告
        this.warnTip = `The maximum allowable input is ${max}₱`;
      }

      return this.errTip;
    },
    // 选择银行账户
    handleCardCheck(item: AccountInfo) {
      this.selectedAccountInfo = item;
      this.showAccountListDialog = false;
      const index = this.withdrawData.findIndex((it) => it.account_type === item.type);
      this.setWithdrawRangeValue(index);
      this.curRechangeMethods = METHODS_NAMES[item.type as keyof typeof METHODS_NAMES];
    },
    // 设置提现的最大、最小范围
    setWithdrawRangeValue(index: number) {
      this.minAmountNum = this.withdrawData[index]?.min || null;
      this.maxAmountNum = this.withdrawData[index]?.max || null;
    },
    // 获取提现方式 及金额配置
    async getWithdrawConfigList(callBack?: () => void) {
      const res = await rechargeWithdraw({
        appChannel: this.CHANEL_TYPE === CHANEL_TYPE.G_CASH ? "Gcash_h5" : this.CHANEL_TYPE,
      });
      if (res && res.withdraw) {
        this.withdrawData = res.withdraw;
        for (let index = 0; index < this.withdrawData.length; index++) {
          this.withdrawData.sort((a, b) => {
            if (a.sort != b.sort) {
              //按sort值从大到小
              return b.sort - a.sort;
            } else {
              //sort值相同按照首字母从大到小排序
              if (a.name && b.name) {
                return b.name.localeCompare(a.name);
              }
              return 0;
            }
          });
        }
        callBack && callBack();
      }
      return this.withdrawData;
    },

    // 1倍流水风险校验
    async handleRiskWithdrawalDialog(callBack: () => void, jumpCheckRisk = false) {
      if (jumpCheckRisk) {
        callBack && callBack();
      } else {
        const response = await checkWithdrawRisk({});
        const { code, data } = response;
        this.riskWithdrawalInfo = data;
        this.riskWithdrawalCallBack = callBack;
        if (code === 200 || code === 0) {
          if (data.status === 0) {
            this.showRiskWithdrawalDialog = true;
          } else if (data?.kyc_status == 1 || data?.kyc_status == 2) {
            this.showKycRemindDialog = true;
          } else {
            callBack && callBack();
          }
        } else {
          callBack && callBack();
        }
      }
    },

    // 提现底部确认入口
    async handleConfirm() {
      try {
        // 小程序没有添加账号，只有当前账号
        if (this.isMiniChannel || this.accounts.length) {
          // 1倍流水风险校验
          // await this.handleRiskWithdrawalDialog(() => this.preVaildFn());
          this.preVaildFn();
        } else {
          this.showWithdrawDialog = false;
          router.push(`/account/withdraw-account`);
        }
      } catch (error) {
        console.error("Withdraw risk check failed:", error);
        this.$dialog({
          title: "Tips",
          message: "Failed to verify withdrawal conditions. Please try again.",
          confirmText: "Done",
          showCancelButton: false,
          onConfirm: () => {
            this.showWithdrawDialog = false;
          },
        });
      }
    },
    // 前置校验
    preVaildFn() {
      if (this.isMiniChannel) {
        // 二次确认弹窗
        this.showMoneyConfirmDialog = true;
      } else if (this.CHANEL_TYPE === CHANEL_TYPE.WEB) {
        const { verifyPreconditions } = useVerifyPreconditions();
        const validResult = verifyPreconditions(() => {
          if (this.isNeedLoginPassword) {
            this.showWithdrawDialog = false;
            this.showVerifyLoginPasswordDialog = true;
          } else {
            this.reqExchange();
          }
        });
        if (!validResult) {
          this.showWithdrawDialog = false;
          this.showVerifyPreconditionsDialog = true;
        }

        //先验证一下kyc ，回调执行以下代码 614行
        // KycMgr.instance.verifyKyc(InGameType.Withdraw, (isVerity) => {
        //   if (isVerity) {
        //     // 理论上这个条件不会执行，因为前置拦截了，必须是有登录密码
        //     const { verifyPreconditions } = useVerifyPreconditions();
        //     const validResult = verifyPreconditions(() => {
        //       if (this.isNeedLoginPassword) {
        //         this.showWithdrawDialog = false;
        //         this.showVerifyLoginPasswordDialog = true;
        //       } else {
        //         this.reqExchange();
        //       }
        //     });
        //     if (!validResult) {
        //       this.showWithdrawDialog = false;
        //       this.showVerifyPreconditionsDialog = true;
        //     }
        //   } else {
        //     this.showWithdrawDialog = false;
        //   }
        // });
      }
    },
    // 密码次数过多限制，复制email
    handlePasswordLimtConfirm() {
      this.showPaymentPassworLimtDialog = false;
    },
    // 登录密码
    handleLoginPasswordSuccConfirm(loginPassword: string) {
      verifyLoginPassword({
        params: {
          withdraw_password: Md5.hashStr(loginPassword).toString(),
        },
        successCallBack: () => {
          this.reqExchange();
        },
        failCallBack: () => {},
      });
    },
    // 绑定手机号码、设置支付密码设置成功
    handlePasswordSuccConfirm() {
      this.showVerifyPreconditionsDialog = false;
      // 关闭弹窗、让其二次发起吧
      // this.reqExchange();
    },
    // 小程序端提现弹窗 确认
    async handleMoneyConfirm() {
      this.showMoneyConfirmDialog = false;
      this.reqExchange();
    },
    // 极验
    reqExchange() {
      GeetestMgr.instance.geetest_device(GEETEST_TYPE.withdraw, (succ: any) => {
        if (succ) {
          let ret = {};
          if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
            ret = succ;
          }
          this.reqExchange_true(ret);
        }
      });
    },
    // 发起提现请求
    async reqExchange_true(ret?: any) {
      try {
        const globalStore = useGlobalStore();
        const params = {
          account_id: (this.selectedAccountInfo as any)?.type || "",
          product_type: PRODUCT_TYPE.MAYA_MINI,
          name: globalStore.userInfo.nickname,
          amount: parseInt(this.selectedAmount),
          price: parseInt(this.selectedAmount),
          quantity: 1,
          geetest_guard: ret?.geetest_guard || "",
          userInfo: ret?.userInfo || "",
          geetest_captcha: ret?.geetest_captcha || "",
          buds: ret?.buds || "64",
        };

        if (this.CHANEL_TYPE == CHANEL_TYPE.WEB) {
          if ((this.selectedAccountInfo as any)?.type == METHODS_ID.Maya) {
            params["product_type"] = PRODUCT_TYPE.WITHDRAW_MAYA_WEB;
          } else if ((this.selectedAccountInfo as any)?.type == METHODS_ID.Gcash) {
            params["product_type"] = PRODUCT_TYPE.WITHDRAW_GCASH_WEB;
          }
        }

        if (this.CHANEL_TYPE == CHANEL_TYPE.G_CASH) {
          params["product_type"] = PRODUCT_TYPE.GCASH;
          params["app_package_name"] = "com.nustargame.gcash";
        }

        const response = await goExchange(params);
        const { code, msg } = response;

        if (code === 200 || code === 0) {
          this.showWithdrawDialog = false;
          this.$dialog({
            title: "Congratulations",
            message:
              "Your withdrawal request has been submitted successfully. You will receive in your account within 10 minutes.",
            confirmText: "Done",
            showCancelButton: false,
            onConfirm: async () => {
              // 更新余额
              globalStore.getBalance();
            },
          });
        } else {
          if (code == MAINTENANCETIPCODE) {
            // 服务器维护页
            this.showWithdrawDialog = false;
            router.push("/system/maintenance");
          } else if (code == 2) {
            // 需要maya登入
            await MobileWindowManager.launchExternalGame(
              async () => response?.url,
              (error) => {
                showToast("Failed to open Maya , please try again");
              }
            );
          } else if (code == 1) {
            this.$dialog({
              title: "Tips",
              message: msg || "Some mistakes occurred.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
            this.showWithdrawDialog = false;
          } else {
            this.$dialog({
              title: "Tips",
              message: msg || "Some mistakes occurred.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
            this.showWithdrawDialog = false;
          }
        }
      } catch (error) {
        console.error("Withdrawal request failed:", error);
        this.showWithdrawDialog = false;
        this.$dialog({
          title: "Tips",
          message: "Network error occurred. Please check your connection and try again.",
          confirmText: "Done",
          showCancelButton: false,
          onConfirm: () => {
            // 可以选择重新打开弹窗或其他操作
          },
        });
      }
    },

    /**拉取不到数据或用户未配置/未选择提现账号 点击按钮给出提示信息 */
    handleInputClick() {
      if (!this.withdrawData || this.withdrawData.length === 0) {
        showToast({
          message: enTranslations.tipword101,
          className: "custom-toast-width",
        });
        return;
      }
      if (!this.accounts || this.accounts.length === 0) {
        showToast({
          message: enTranslations.tipword99,
        });
        return;
      }
      let filter_list = this.withdrawData.filter((item) => {
        return item.account_type == this.selectedAccountInfo.type;
      });
      if (filter_list.length == 0) {
        let tips_msg = enTranslations.tipword102;
        if (this.CHANEL_TYPE == CHANEL_TYPE.G_CASH) {
          tips_msg = enTranslations.tipword103;
        }
        showToast({
          message: tips_msg,
        });
        return;
      }
    },

    resetData() {
      this.selectedAmount = "";
      this.errTip = "";
    },
  },
});
