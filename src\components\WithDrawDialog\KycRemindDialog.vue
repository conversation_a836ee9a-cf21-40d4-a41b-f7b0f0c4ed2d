<template>
  <ZDialog
    v-model="showKycRemindDialog"
    title="KYC Verification"
    :show-close="true"
    :confirm-text="confirmText"
    cancel-text="Verify Now"
    :on-confirm="handleConfirm"
    :on-cancel="handleCancel"
    :showCancelButton="!forceValidateKyc"
  >
    <div class="content">
      <div class="title" v-if="!forceValidateKyc">
        <span class="text">Not Verified</span>
      </div>
      <div class="desc">Complete your KYC before withdrawal.</div>
    </div>
  </ZDialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from "vue";
import { storeToRefs } from "pinia";
import { useWithdrawStore } from "@/stores/withdraw";
import { KycMgr, InGameType } from "@/utils/KycMgr";

const withdrawStore = useWithdrawStore();
const { showKycRemindDialog, riskWithdrawalInfo, riskWithdrawalCallBack } =
  storeToRefs(withdrawStore);

const forceValidateKyc = computed(() => {
  return riskWithdrawalInfo.value?.kyc_status == 2;
});

const confirmText = computed(() => {
  if (forceValidateKyc.value) {
    return "Verify Now";
  }
  return "Continue";
});

const handleConfirm = async () => {
  if (forceValidateKyc.value) {
    const res = await KycMgr.instance.verifyKyc(InGameType.UNKNOWN);
    KycMgr.instance.checkBindPhone();
  } else {
    withdrawStore.showKycRemindDialog = false;
    if (riskWithdrawalCallBack.value) {
      riskWithdrawalCallBack.value();
    }
  }
};
const handleCancel = () => {
  withdrawStore.showKycRemindDialog = false;
};
</script>

<style lang="scss" scoped>
.content {
  .title {
    text-align: center;
    margin-bottom: 12px;
    .text {
      font-size: 18px;
      color: #222;
      font-weight: 700;
      color: #ac1140;
      background: #fcf3f5;
      padding: 5px 15px;
      border-radius: 30px;
    }
  }
  .desc {
    font-size: 14px;
    color: #222;
    text-align: center;
  }
}
</style>
